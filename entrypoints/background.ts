import { defineBackground } from 'wxt/utils/define-background';
import * as jsondiffpatch from 'jsondiffpatch';

// 类型定义
interface InterceptRule {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
  conditions: {
    urlPattern: string;
    urlMatchType: 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exact';
    methods?: string[];
  };
  transformation: {
    newUrl: string;
    paramMapping?: Record<string, string>;
    preserveOriginalParams?: boolean;
    includeCookies?: boolean;
    autoCopyCookies?: boolean;
    cookiesToCopy?: string[];
    cookieCopyStrategy?: 'all' | 'specified' | 'auth-only';
  };
  diffConfig?: {
    ignoreFields?: string[];
  };
}

interface DiffReport {
  id: string;
  ruleId: string;
  ruleName: string;
  timestamp: number;
  request: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
    newUrl?: string;
  };
  responses: {
    old: { status: number; body: any; responseTime: number; error?: string };
    new: { status: number; body: any; responseTime: number; error?: string };
  };
  diff: {
    delta: any;
    hasChanges: boolean;
    changeCount: number;
    severity: 'none' | 'minor' | 'major' | 'critical';
  };
  visualizations: {
    html: string;
    summary?: string;
  };
}

export default defineBackground(() => {
  console.log('🚀 API迁移验证Service Worker启动');

  // 消息监听器
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 收到消息:', request);

    if (request.type === 'ping') {
      console.log('🏓 响应ping');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }

    // API迁移消息处理
    if (request.type === 'api-migration') {
      console.log('🎯 处理API迁移消息...');
      handleApiMigrationMessage(request, sender, sendResponse).catch(error => {
        console.error('处理API迁移消息异常:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true;
    }

    // XUID相关消息处理（暂时忽略，避免"未知操作"错误）
    if (request.action === 'xuidDetected' || request.action === 'userInfoDetected') {
      console.log('📋 收到XUID相关消息，暂时忽略:', request.action);
      sendResponse({ success: true, message: 'XUID消息已收到但暂未处理' });
      return true;
    }

    // 对于没有明确type的消息，不处理
    if (!request.type) {
      console.log('⚠️ 收到没有type字段的消息，忽略:', request);
      return false;
    }

    return false;
  });

  // 安装事件
  browser.runtime.onInstalled.addListener(() => {
    console.log('✅ Service Worker已安装');
  });

  // 启动事件
  browser.runtime.onStartup.addListener(() => {
    console.log('🔄 Service Worker已启动');
  });

  console.log('✅ Service Worker初始化完成');

  // 初始化API迁移功能
  initApiMigration().then(() => {
    console.log('✅ API迁移后台功能初始化完成');
  }).catch((error: any) => {
    console.error('❌ API迁移后台功能初始化失败:', error);
  });
});

// API迁移功能变量
let apiMigrationRules: InterceptRule[] = [];
let isIntercepting = false;
let activeRequests = new Map<string, any>(); // 跟踪活跃的请求
let processingRequests = new Set<string>(); // 跟踪正在处理的请求URL，防止死循环

// 域名过滤配置
const domainFilterConfig = {
  enabled: true, // 是否启用域名过滤
  mode: 'whitelist' as 'whitelist' | 'blacklist', // 白名单或黑名单模式
  domains: new Set<string>(), // 缓存的域名列表
  patterns: new Set<string>() // 缓存的URL模式
};

// 预定义的常见API域名（当没有规则时使用）
const commonApiDomains = [
  // 开发测试域名
  'localhost',
  '127.0.0.1',
  'assistantdesk-base-cc.suanshubang.cc',
];

// 初始化JSON差异对比器
const differ = jsondiffpatch.create({
  objectHash: function(obj: any) {
    return obj.id || obj._id || obj.key || JSON.stringify(obj);
  },
  arrays: {
    detectMove: true,
    includeValueOnMove: false
  }
});

// 设置全局jsondiffpatch实例以供其他模块使用
(globalThis as any).jsondiffpatch = jsondiffpatch;

// 初始化API迁移功能
async function initApiMigration() {
  console.log('🔧 API迁移验证工具后台功能已初始化');
  console.log('📋 开始加载API迁移规则...');
  await loadApiMigrationRules();
  console.log('✅ API迁移规则加载完成');

  // 加载拦截器状态
  await loadInterceptingStatus();
  console.log('✅ 拦截器状态加载完成');

  // 更新域名过滤器
  updateDomainFilter();
}

// 更新域名过滤器
function updateDomainFilter() {
  if (!domainFilterConfig.enabled) {
    console.log('🔍 域名过滤已禁用，将拦截所有请求');
    return;
  }

  // 清空现有的域名和模式
  domainFilterConfig.domains.clear();
  domainFilterConfig.patterns.clear();

  // 从规则中提取域名和模式
  for (const rule of apiMigrationRules) {
    if (!rule.enabled) continue;

    const domains = extractDomainsFromRule(rule);
    domains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加域名过滤: ${domain}`);
    });
  }

  // 如果没有从规则中提取到域名，使用预定义的常见API域名
  if (domainFilterConfig.domains.size === 0) {
    console.log('📝 未从规则中提取到域名，使用预定义的常见API域名');
    commonApiDomains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加预定义域名: ${domain}`);
    });
  }

  console.log(`✅ 域名过滤器已更新，共 ${domainFilterConfig.domains.size} 个域名`);
}

// 从规则中提取域名
function extractDomainsFromRule(rule: InterceptRule): string[] {
  const domains: string[] = [];
  const urlPattern = rule.conditions.urlPattern;

  try {
    // 处理不同的URL模式
    switch (rule.conditions.urlMatchType) {
      case 'exact':
        const exactDomain = extractDomainFromUrl(urlPattern);
        if (exactDomain) domains.push(exactDomain);
        break;

      case 'startsWith':
        if (urlPattern.startsWith('http://') || urlPattern.startsWith('https://')) {
          const domain = extractDomainFromUrl(urlPattern);
          if (domain) domains.push(domain);
        }
        break;

      case 'contains':
        // 尝试从包含的字符串中提取域名
        const containsDomain = extractDomainFromPattern(urlPattern);
        if (containsDomain) domains.push(containsDomain);
        break;

      case 'regex':
        // 从正则表达式中提取域名（简单处理）
        const regexDomains = extractDomainsFromRegex(urlPattern);
        domains.push(...regexDomains);
        break;

      case 'endsWith':
        // 对于endsWith，通常是路径匹配，不太适合域名提取
        break;
    }

    // 同时处理transformation.newUrl中的域名
    const newDomain = extractDomainFromUrl(rule.transformation.newUrl);
    if (newDomain && !domains.includes(newDomain)) {
      domains.push(newDomain);
    }
  } catch (error) {
    console.warn(`⚠️ 从规则 ${rule.name} 提取域名失败:`, error);
  }

  return domains;
}


// 从模式中提取域名
function extractDomainFromPattern(pattern: string): string | null {
  // 查找类似域名的模式
  const domainMatch = pattern.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/);
  return domainMatch ? domainMatch[0] : null;
}

// 从正则表达式中提取域名（简单实现）
function extractDomainsFromRegex(regex: string): string[] {
  const domains: string[] = [];

  // 查找正则中的域名模式
  const domainMatches = regex.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g);
  if (domainMatches) {
    domains.push(...domainMatches);
  }

  return domains;
}

// 获取指定域的Cookie - 优化版本
async function getDomainCookies(domain: string): Promise<string> {
  console.log('🍪 开始获取域Cookie:', { domain });
  
  try {
    // 检查是否有cookies权限
    if (!browser.cookies) {
      console.warn('⚠️ browser.cookies API不可用');
      return '';
    }
    
    if (!browser.cookies.getAll) {
      console.warn('⚠️ browser.cookies.getAll 方法不可用');
      return '';
    }
    
    console.log('✅ Cookie API可用，开始获取Cookie:', { domain });
    
    // 获取所有可访问的Cookie
    const allCookies = await browser.cookies.getAll({});
    console.log('🌐 所有Cookie数量:', allCookies.length);
    console.log('🌐 所有Cookie域名:', [...new Set(allCookies.map((c: any) => c.domain))]);
    
    // 过滤出目标域的Cookie
    const targetCookies = allCookies.filter((cookie: any) => {
      const cookieDomain = cookie.domain.replace(/^\./, ''); // 移除开头的点
      const targetDomain = domain.replace(/^\./, '');
      
      // 精确匹配或子域名匹配
      const isMatch = cookieDomain === targetDomain || 
                     cookieDomain.endsWith('.' + targetDomain) ||
                     targetDomain.endsWith('.' + cookieDomain);
      
      console.log('🔍 Cookie域名匹配检查:', {
        cookieName: cookie.name,
        cookieDomain,
        targetDomain,
        isMatch,
        exactMatch: cookieDomain === targetDomain,
        cookieEndsWith: cookieDomain.endsWith('.' + targetDomain),
        targetEndsWith: targetDomain.endsWith('.' + cookieDomain)
      });
      
      return isMatch;
    });
    
    console.log('🎯 目标域Cookie数量:', targetCookies.length);
    console.log('🎯 目标域Cookie详情:', targetCookies.map((c: any) => ({
      name: c.name,
      domain: c.domain,
      value: c.value ? c.value.substring(0, 20) + '...' : 'null',
      path: c.path,
      secure: c.secure,
      httpOnly: c.httpOnly
    })));
    
    if (targetCookies.length > 0) {
      console.log('🍪 找到的Cookie:', targetCookies.map((c: any) => c.name));
      const cookieString = targetCookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');
      console.log('🍪 生成的Cookie字符串长度:', cookieString.length);
      return cookieString;
    }
    
    console.warn('⚠️ 未找到域Cookie:', domain);
    console.log('🔍 可访问的域名列表:', [...new Set(allCookies.map((c: any) => c.domain))]);
    return '';
    
  } catch (error: any) {
    console.error('❌ 获取域Cookie失败:', {
      domain,
      error: error?.message,
      stack: error?.stack
    });
    return '';
  }
}

// 复制原始域的认证Cookie到目标域
async function copyAuthCookies(
  originalDomain: string, 
  targetDomain: string, 
  cookiesToCopy: string[] = ['sessionid', 'token', 'auth', 'session', 'login', 'jwt', 'access_token', 'refresh_token']
): Promise<number> {
  console.log('🔄 开始复制认证Cookie:', { 
    originalDomain, 
    targetDomain, 
    cookiesToCopy,
    targetDomainType: typeof targetDomain,
    originalDomainType: typeof originalDomain 
  });
  
  try {
    // 检查是否有cookies权限
    if (!browser.cookies || !browser.cookies.getAll || !browser.cookies.set) {
      console.warn('⚠️ Cookie API不可用，无法复制Cookie');
      return 0;
    }
    
    // 获取原始域的所有Cookie
    const originalCookies = await browser.cookies.getAll({});
    console.log('🌐 原始域Cookie总数:', originalCookies.length);
    console.log('🌐 原始域Cookie详情:', originalCookies.map((c: any) => ({ 
      name: c.name, 
      domain: c.domain, 
      value: c.value ? c.value.substring(0, 20) + '...' : 'null' 
    })));
    
    // 过滤出原始域的认证Cookie
    const authCookies = originalCookies.filter((cookie: any) => {
      const cookieDomain = cookie.domain.replace(/^\./, '');
      const normalizedOriginalDomain = originalDomain.replace(/^\./, '');
      
      // 检查域名匹配 - 添加详细日志
      const domainMatch = cookieDomain === normalizedOriginalDomain || 
                         cookieDomain.endsWith('.' + normalizedOriginalDomain) ||
                         normalizedOriginalDomain.endsWith('.' + cookieDomain);
      
      console.log('🔍 域名匹配检查:', {
        cookieName: cookie.name,
        cookieDomain,
        normalizedOriginalDomain,
        domainMatch,
        cookieDomainEndsWith: cookieDomain.endsWith('.' + normalizedOriginalDomain),
        originalEndsWith: normalizedOriginalDomain.endsWith('.' + cookieDomain)
      });
      
      // 检查是否为认证相关的Cookie
      const isAuthCookie = cookiesToCopy.some(pattern => 
        cookie.name.toLowerCase().includes(pattern.toLowerCase())
      );
      
      // 安全检查：排除敏感信息
      const isSafe = !cookie.name.toLowerCase().includes('password') && 
                     !cookie.name.toLowerCase().includes('credit') &&
                     !cookie.name.toLowerCase().includes('card') &&
                     !cookie.name.toLowerCase().includes('secret');
      
      const shouldInclude = domainMatch && isAuthCookie && isSafe;
      console.log('🔍 Cookie包含检查:', {
        cookieName: cookie.name,
        domainMatch,
        isAuthCookie,
        isSafe,
        shouldInclude
      });
      
      return shouldInclude;
    });
    
    console.log('🎯 找到的认证Cookie:', authCookies.map((c: any) => ({ 
      name: c.name, 
      domain: c.domain, 
      value: c.value ? c.value.substring(0, 20) + '...' : 'null',
      path: c.path,
      secure: c.secure,
      httpOnly: c.httpOnly
    })));
    
    if (authCookies.length === 0) {
      console.log('⚠️ 未找到可复制的认证Cookie');
      return 0;
    }
    
    // 复制Cookie到目标域
    let copiedCount = 0;
    for (const cookie of authCookies) {
      try {
        // 构建目标域的Cookie设置 - 特殊处理localhost
        let cookieUrl, cookieDomain;
        
        if (targetDomain.includes('127.0.0.1') || targetDomain.includes('localhost')) {
          // 对于localhost/127.0.0.1，使用http并设置domain为null
          cookieUrl = `http://${targetDomain}${cookie.path || '/'}`;
          cookieDomain = null; // 让浏览器自动处理
        } else {
          // 对于其他域名，使用https
          cookieUrl = `https://${targetDomain}${cookie.path || '/'}`;
          cookieDomain = targetDomain;
        }
        
        const cookieDetails: any = {
          url: cookieUrl,
          name: cookie.name,
          value: cookie.value,
          domain: cookieDomain,
          path: cookie.path || '/',
          secure: targetDomain.includes('127.0.0.1') || targetDomain.includes('localhost') ? false : cookie.secure,
          httpOnly: cookie.httpOnly,
          sameSite: cookie.sameSite || 'Lax'
        };
        
        // 如果有过期时间，保留它
        if (cookie.expirationDate) {
          cookieDetails.expirationDate = cookie.expirationDate;
        }
        
        console.log('🍪 尝试设置Cookie:', {
          name: cookie.name,
          url: cookieUrl,
          domain: cookieDomain,
          path: cookie.path || '/',
          secure: targetDomain.includes('127.0.0.1') || targetDomain.includes('localhost') ? false : cookie.secure,
          httpOnly: cookie.httpOnly,
          sameSite: cookie.sameSite || 'Lax',
          hasExpiration: !!cookie.expirationDate,
          isLocalhost: targetDomain.includes('127.0.0.1') || targetDomain.includes('localhost')
        });
        
        await browser.cookies.set(cookieDetails);
        copiedCount++;
        console.log(`✅ Cookie复制成功: ${cookie.name} -> ${targetDomain}`);
        
        // 验证Cookie是否真的设置成功
        try {
          // 对于localhost，使用URL来验证Cookie
          const verifyUrl = targetDomain.includes('127.0.0.1') || targetDomain.includes('localhost') 
            ? `http://${targetDomain}/` 
            : `https://${targetDomain}/`;
          
          const verifyCookies = await browser.cookies.getAll({
            url: verifyUrl,
            name: cookie.name
          });
          console.log(`🔍 验证Cookie ${cookie.name} 设置结果:`, {
            success: verifyCookies.length > 0,
            cookieCount: verifyCookies.length,
            verifyUrl
          });
        } catch (verifyError) {
          console.warn(`⚠️ 验证Cookie ${cookie.name} 失败:`, verifyError);
        }
        
      } catch (error: any) {
        console.warn(`⚠️ Cookie复制失败: ${cookie.name}`, {
          error: error?.message,
          stack: error?.stack,
          cookieDetails: {
            name: cookie.name,
            domain: targetDomain,
            url: `https://${targetDomain}${cookie.path || '/'}`
          }
        });
      }
    }
    
    console.log(`🎉 Cookie复制完成: ${copiedCount}/${authCookies.length} 个Cookie成功复制`);
    return copiedCount;
    
  } catch (error: any) {
    console.error('❌ Cookie复制失败:', {
      originalDomain,
      targetDomain,
      error: error?.message,
      stack: error?.stack
    });
    return 0;
  }
}

// 从URL提取域名
function extractDomainFromUrl(url: string): string | null {
  console.log('🔍 提取域名:', { url });
  
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    console.log('✅ 成功提取域名:', { url, hostname });
    return hostname;
  } catch (error: any) {
    console.log('⚠️ URL解析失败，尝试其他方法:', { url, error: error?.message });
    // 如果不是完整URL，尝试其他方法
    const match = url.match(/(?:https?:\/\/)?([^\/\s?#]+)/);
    const result = match ? match[1] : null;
    console.log('🔍 正则匹配结果:', { url, match: match?.[0], result });
    return result;
  }
}

// 处理CORS错误
async function handleCorsError(error: any, rule: InterceptRule) {
  if (error.isCorsError) {
    console.warn('🌐 检测到CORS错误');
    
    // 显示友好的错误提示
    try {
      // 这里可以添加通知逻辑
      console.log('🔔 跨域请求错误提示:', {
        title: '跨域请求错误',
        message: `新接口 ${rule.transformation.newUrl} 可能存在CORS限制，请检查服务器配置或扩展权限`
      });
    } catch (notificationError) {
      console.warn('⚠️ 无法发送通知:', notificationError);
    }
    
    return {
      status: 0,
      body: null,
      responseTime: 0,
      error: '跨域请求失败：目标服务器未允许跨域访问',
      suggestion: '请检查目标服务器的CORS配置或扩展权限设置'
    };
  }
  
  return error;
}

// 检查URL是否应该被拦截
function shouldInterceptUrl(url: string): boolean {
  if (!domainFilterConfig.enabled) {
    return true; // 如果禁用过滤，拦截所有请求
  }

  if (domainFilterConfig.domains.size === 0) {
    return true; // 如果没有配置域名，拦截所有请求
  }

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;

    // 检查精确匹配
    if (domainFilterConfig.domains.has(hostname)) {
      return true;
    }

    // 检查子域名匹配
    for (const domain of domainFilterConfig.domains) {
      if (hostname.endsWith('.' + domain) || hostname === domain) {
        return true;
      }
    }

    return domainFilterConfig.mode === 'blacklist'; // 黑名单模式下，不在列表中的允许通过
  } catch {
    return true; // URL解析失败时，保守地拦截
  }
}

// 加载API迁移规则
async function loadApiMigrationRules() {
  try {
    const result = await browser.storage.local.get(['apiMigrationRules']);
    apiMigrationRules = result.apiMigrationRules || [];
    console.log('📋 已加载', apiMigrationRules.length, '条API迁移规则');
  } catch (error) {
    console.error('加载API迁移规则失败:', error);
    apiMigrationRules = [];
  }
}

// 保存API迁移规则
async function saveApiMigrationRules() {
  try {
    await browser.storage.local.set({ apiMigrationRules });
    console.log('💾 API迁移规则已保存');
  } catch (error) {
    console.error('保存API迁移规则失败:', error);
  }
}

// 加载拦截器状态
async function loadInterceptingStatus() {
  try {
    const result = await browser.storage.local.get(['isIntercepting']);
    isIntercepting = result.isIntercepting || false;
    console.log('📋 已加载拦截器状态:', isIntercepting ? '运行中' : '已停止');

    // 如果状态为运行中，重新注册监听器
    if (isIntercepting) {
      console.log('🔄 重新注册webRequest监听器...');
      if (browser.webRequest && browser.webRequest.onBeforeRequest) {
        browser.webRequest.onBeforeRequest.addListener(
          handleBeforeRequest,
          { urls: ['<all_urls>'] },
          ['requestBody']
        );
        console.log('✅ webRequest.onBeforeRequest 监听器已重新注册');
      }
    }
  } catch (error) {
    console.error('加载拦截器状态失败:', error);
    isIntercepting = false;
  }
}

// 保存拦截器状态
async function saveInterceptingStatus() {
  try {
    await browser.storage.local.set({ isIntercepting });
    console.log('💾 拦截器状态已保存:', isIntercepting ? '运行中' : '已停止');
  } catch (error) {
    console.error('保存拦截器状态失败:', error);
  }
}

// 处理API迁移消息
async function handleApiMigrationMessage(request: any, _sender: any, sendResponse: any) {
  console.log('🔍 处理API迁移消息，完整请求对象:', JSON.stringify(request, null, 2));

  // 兼容两种消息格式：新格式 {type, action, data} 和旧格式 {type, action, ...data}
  const action = request.action;
  const data = request.data || request; // 如果没有data字段，使用整个request对象

  console.log('🎯 解构后的action:', action);
  console.log('🎯 解构后的data:', data);

  // 检查action是否存在
  if (!action) {
    console.log('❌ action字段为空或未定义');
    sendResponse({ success: false, error: 'action字段缺失' });
    return;
  }

  switch (action) {
    case 'getRules':
      console.log('✅ 处理getRules请求，返回', apiMigrationRules.length, '条规则');
      sendResponse({ success: true, data: apiMigrationRules });
      break;

    case 'addRule':
      const newRule: InterceptRule = {
        ...data,
        id: 'rule_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      apiMigrationRules.push(newRule);
      await saveApiMigrationRules();
      updateDomainFilter();
      sendResponse({ success: true, data: newRule });
      break;

    case 'updateRule':
      const ruleIndex = apiMigrationRules.findIndex(r => r.id === data.id);
      if (ruleIndex !== -1) {
        apiMigrationRules[ruleIndex] = { ...data, updatedAt: Date.now() };
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true, data: apiMigrationRules[ruleIndex] });
      } else {
        sendResponse({ success: false, error: '规则不存在' });
      }
      break;

    case 'deleteRule':
      const deleteIndex = apiMigrationRules.findIndex(r => r.id === data.id);
      if (deleteIndex !== -1) {
        apiMigrationRules.splice(deleteIndex, 1);
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: '规则不存在' });
      }
      break;

    case 'startIntercepting':
      await startIntercepting();
      sendResponse({ success: true });
      break;

    case 'stopIntercepting':
      await stopIntercepting();
      sendResponse({ success: true });
      break;

    case 'getReports':
      const reports = await getReports();
      sendResponse({ success: true, data: reports });
      break;

    case 'clearReports':
      await clearReports();
      sendResponse({ success: true });
      break;

    case 'updateRules':
      if (data && data.rules) {
        apiMigrationRules = data.rules;
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: '无效的规则数据' });
      }
      break;

    case 'getStatus':
      console.log('✅ 处理getStatus请求');
      sendResponse({
        success: true,
        data: {
          isIntercepting: isIntercepting,
          rulesCount: apiMigrationRules.length
        }
      });
      break;

    default:
      console.log('❌ 未知操作:', action, '完整请求:', request);
      sendResponse({ success: false, error: '未知操作' });
  }
}

// 启动拦截
async function startIntercepting() {
  if (isIntercepting) {
    console.log('⚠️ 拦截已经在运行中');
    return;
  }

  try {
    // 注册webRequest监听器
    if (browser.webRequest && browser.webRequest.onBeforeRequest) {
      browser.webRequest.onBeforeRequest.addListener(
        handleBeforeRequest,
        { urls: ['<all_urls>'] },
        ['requestBody']
      );
      console.log('✅ webRequest.onBeforeRequest 监听器已注册');
    }

    isIntercepting = true;
    await saveInterceptingStatus();
    console.log('🚀 API拦截已启动');
  } catch (error) {
    console.error('❌ 启动API拦截失败:', error);
    throw error;
  }
}

// 停止拦截
async function stopIntercepting() {
  if (!isIntercepting) {
    console.log('⚠️ 拦截未在运行');
    return;
  }

  try {
    // 移除webRequest监听器
    if (browser.webRequest && browser.webRequest.onBeforeRequest) {
      browser.webRequest.onBeforeRequest.removeListener(handleBeforeRequest);
      console.log('✅ webRequest.onBeforeRequest 监听器已移除');
    }

    isIntercepting = false;
    await saveInterceptingStatus();
    console.log('🛑 API拦截已停止');
  } catch (error) {
    console.error('❌ 停止API拦截失败:', error);
    throw error;
  }
}

// 增强版请求拦截处理
function handleBeforeRequest(details: any): undefined {
  // 只处理主要的API请求
  if (details.type !== 'main_frame' && details.type !== 'xmlhttprequest' && details.type !== 'fetch') {
    return undefined;
  }

  // 检查是否为内部并行对比请求，避免死循环
  try {
    const url = new URL(details.url);
    if (url.searchParams.has('__api_migration_internal__')) {
      return undefined;
    }
  } catch (error) {
    // URL解析失败，继续处理
  }

  // 域名过滤检查
  if (!shouldInterceptUrl(details.url)) {
    return undefined;
  }

  // 检查是否已经在处理相同的URL，防止死循环
  if (processingRequests.has(details.url)) {
    return undefined;
  }

  console.log('🔍 处理API请求:', details.url);

  const rule = findMatchingRule(details.url, details.method);
  if (!rule) {
    return undefined;
  }

  console.log('✅ 找到匹配规则:', rule.name);

  // 标记请求正在处理
  processingRequests.add(details.url);

  // 记录请求信息
  activeRequests.set(details.requestId, {
    url: details.url,
    method: details.method,
    rule: rule,
    timestamp: Date.now(),
    requestBody: details.requestBody
  });

  try {
    // 并行对比模式 - 真正的并行请求
    console.log('🔄 启动并行对比模式');
    performEnhancedParallelComparison(details, rule).catch((error: any) => {
      console.error('并行对比失败:', error);
    }).finally(() => {
      // 处理完成后清理标记
      processingRequests.delete(details.url);
    });

  } catch (error) {
    console.error('处理API请求失败:', error);
    // 发生错误时也要清理标记
    processingRequests.delete(details.url);
  }

  return undefined;
}

// 查找匹配的规则
function findMatchingRule(url: string, method: string) {
  const enabledRules = apiMigrationRules.filter(rule => rule.enabled)
    .sort((a, b) => b.priority - a.priority);

  for (const rule of enabledRules) {
    if (isRuleMatching(rule, url, method)) {
      return rule;
    }
  }
  return null;
}

// 检查规则是否匹配
function isRuleMatching(rule: InterceptRule, url: string, method: string): boolean {
  // 检查HTTP方法
  if (rule.conditions.methods && rule.conditions.methods.length > 0) {
    if (!rule.conditions.methods.includes(method.toUpperCase())) {
      return false;
    }
  }

  // 检查URL模式
  const pattern = rule.conditions.urlPattern;
  const matchType = rule.conditions.urlMatchType;

  switch (matchType) {
    case 'exact':
      return url === pattern;
    case 'contains':
      return url.includes(pattern);
    case 'startsWith':
      return url.startsWith(pattern);
    case 'endsWith':
      return url.endsWith(pattern);
    case 'regex':
      try {
        const regex = new RegExp(pattern);
        return regex.test(url);
      } catch (error) {
        console.warn('正则表达式无效:', pattern, error);
        return false;
      }
    default:
      return false;
  }
}

// 获取报告
async function getReports(): Promise<DiffReport[]> {
  try {
    // 使用与api-migration-validator.ts相同的storage key格式
    const storageKey = 'tool-api-migration-validator-apiMigrationReports';
    const result = await browser.storage.local.get([storageKey]);
    const reports = result[storageKey] || [];
    
    console.log('📋 获取报告完成:', {
      storageKey,
      报告数量: reports.length,
      包含新接口URL的报告数量: reports.filter((r: DiffReport) => r.request.newUrl).length
    });
    
    return reports;
  } catch (error) {
    console.error('❌ 获取报告失败:', error);
    return [];
  }
}

// 清除报告
async function clearReports() {
  try {
    // 使用与api-migration-validator.ts相同的storage key格式
    const storageKey = 'tool-api-migration-validator-apiMigrationReports';
    await browser.storage.local.remove([storageKey]);
    console.log('✅ 报告已清除');
  } catch (error) {
    console.error('清除报告失败:', error);
  }
}

// 保存报告到存储
async function saveReportToStorage(report: DiffReport) {
  try {
    console.log('💾 开始保存报告:', {
      id: report.id,
      ruleName: report.ruleName,
      originalUrl: report.request.url,
      newUrl: report.request.newUrl,
      timestamp: report.timestamp
    });

    const reports = await getReports();
    reports.push(report);

    // 限制报告数量，保留最新的100个
    if (reports.length > 100) {
      reports.splice(0, reports.length - 100);
    }

    // 使用与api-migration-validator.ts相同的storage key格式
    const storageKey = 'tool-api-migration-validator-apiMigrationReports';
    await browser.storage.local.set({ [storageKey]: reports });
    
    console.log('✅ 报告已保存:', {
      reportId: report.id,
      包含新接口URL: !!report.request.newUrl,
      新接口URL: report.request.newUrl,
      存储中的报告总数: reports.length
    });
  } catch (error) {
    console.error('❌ 保存报告失败:', error);
  }
}

// 增强版并行对比功能 - 真正的并行请求
async function performEnhancedParallelComparison(details: any, rule: InterceptRule) {
  const startTime = Date.now();
  console.log('🔄 开始并行对比:', details.url);

  try {
    // 构建新URL
    const newUrl = transformUrl(details.url, rule);
    console.log('🎯 目标URL:', newUrl);

    // 准备请求头
    let headers = transformHeaders(details.requestHeaders || {}, rule);
    
    // 如果启用了Cookie且是跨域请求，尝试获取目标域Cookie
    if (rule.transformation.includeCookies) {
      console.log('🔍 开始Cookie处理，includeCookies设置为true');
      
      const originalDomain = extractDomainFromUrl(details.url);
      const newDomain = extractDomainFromUrl(newUrl);
      
      console.log('🌐 域名提取结果:', {
        originalUrl: details.url,
        newUrl: newUrl,
        originalDomain,
        newDomain,
        isCrossDomain: originalDomain !== newDomain
      });
      
      if (originalDomain && newDomain && originalDomain !== newDomain) {
        console.log('🌐 检测到跨域请求，开始获取目标域Cookie:', { 
          originalDomain, 
          newDomain,
          originalDomainType: typeof originalDomain,
          newDomainType: typeof newDomain
        });
        
        // 添加Cookie限制说明
        console.log('📋 Cookie API限制说明:');
        console.log('  - 浏览器扩展只能访问当前已打开页面所在域的Cookie');
        console.log('  - 需要先访问目标域名页面才能获取该域的Cookie');
        console.log('  - 这是由浏览器安全策略决定的，不是扩展的bug');
        
        // 尝试获取目标域Cookie
        const targetCookies = await getDomainCookies(newDomain);
        if (targetCookies) {
          headers['cookie'] = targetCookies;
          console.log('✅ 已添加目标域Cookie:', {
            cookieLength: targetCookies.length,
            cookiePreview: targetCookies.substring(0, 100) + '...'
          });
        } else {
          console.log('❌ 未获取到目标域Cookie，尝试自动复制认证Cookie...');
          
          // 检查是否启用自动Cookie复制
          if (rule.transformation.autoCopyCookies) {
            // 根据策略确定要复制的Cookie
            let cookiesToCopy: string[] = ['sessionid', 'token', 'auth', 'session', 'login', 'jwt', 'access_token', 'refresh_token'];
            
            if (rule.transformation.cookieCopyStrategy === 'specified' && rule.transformation.cookiesToCopy) {
              cookiesToCopy = rule.transformation.cookiesToCopy;
            } else if (rule.transformation.cookieCopyStrategy === 'all') {
              cookiesToCopy = []; // 空数组表示复制所有认证相关Cookie（使用默认值）
            }
            // 'auth-only' 是默认策略，使用默认的cookiesToCopy
            
            // 尝试自动复制认证Cookie
            const copiedCount = await copyAuthCookies(originalDomain, newDomain, cookiesToCopy);
            
            if (copiedCount > 0) {
              // 等待一段时间让Cookie设置完成
              console.log('⏳ 等待Cookie设置完成...');
              await new Promise(resolve => setTimeout(resolve, 500));
              
              // 重新尝试获取目标域Cookie
              const targetCookiesAfterCopy = await getDomainCookies(newDomain);
              if (targetCookiesAfterCopy) {
                headers['cookie'] = targetCookiesAfterCopy;
                console.log('✅ Cookie复制成功，已添加到请求:', {
                  copiedCount,
                  cookieLength: targetCookiesAfterCopy.length,
                  cookiePreview: targetCookiesAfterCopy.substring(0, 100) + '...'
                });
              } else {
                console.log('⚠️ Cookie复制后仍无法获取目标域Cookie');
                
                // 尝试直接从浏览器Cookie存储获取
                try {
                  console.log('🔍 尝试直接从浏览器Cookie存储获取...');
                  const directCookies = await browser.cookies.getAll({
                    domain: newDomain.includes('127.0.0.1') ? null : newDomain,
                    url: newDomain.includes('127.0.0.1') ? `http://${newDomain}/` : `https://${newDomain}/`
                  });
                  console.log('🔍 直接获取结果:', {
                    cookieCount: directCookies.length,
                    cookieNames: directCookies.map((c: any) => c.name)
                  });
                  
                  if (directCookies.length > 0) {
                    const directCookieString = directCookies.map((c: any) => `${c.name}=${c.value}`).join('; ');
                    headers['cookie'] = directCookieString;
                    console.log('✅ 通过直接获取方式添加Cookie成功:', {
                      cookieLength: directCookieString.length,
                      cookiePreview: directCookieString.substring(0, 100) + '...'
                    });
                  }
                } catch (directError) {
                  console.warn('⚠️ 直接获取Cookie失败:', directError);
                }
              }
            } else {
              console.log('❌ Cookie复制失败，请手动处理');
            }
          } else {
            console.log('❌ Cookie复制功能未启用，请手动处理');
          }
          
          console.log('💡 手动解决方案:');
          console.log('  1. 先在浏览器中打开目标域名的页面');
          console.log('  2. 确保在该域名下有登录状态或Cookie');
          console.log('  3. 然后重新触发API迁移对比');
          
          // 如果无法获取目标域Cookie，尝试使用原始Cookie
          const originalCookies = await getDomainCookies(originalDomain);
          if (originalCookies) {
            console.log('🔄 可用的原始Cookie:', {
              cookieLength: originalCookies.length,
              cookiePreview: originalCookies.substring(0, 100) + '...'
            });
            // 可以选择是否添加原始Cookie，这里注释掉避免混淆
            // headers['cookie'] = originalCookies;
          }
        }
      } else {
        console.log('🔍 非跨域请求或域名提取失败，跳过Cookie处理');
      }
    } else {
      console.log('🔍 Cookie处理已跳过，includeCookies设置为false');
    }

    // 准备请求体
    let requestBody = details.requestBody;
    if (requestBody && requestBody.raw) {
      // 处理原始请求体
      const decoder = new TextDecoder();
      requestBody = decoder.decode(requestBody.raw[0].bytes);
    }

    // 并行发送两个请求
    const [oldResponse, newResponse] = await Promise.allSettled([
      makeRequest(details.url, details.method, headers, requestBody, rule),
      makeRequest(newUrl, details.method, headers, requestBody, rule)
    ]);

    // 处理响应结果
    const oldResult = oldResponse.status === 'fulfilled' ? oldResponse.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: oldResponse.reason?.message || '请求失败'
    };

    const newResult = newResponse.status === 'fulfilled' ? newResponse.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: newResponse.reason?.message || '请求失败'
    };

    // 生成差异报告
    const diffResult = generateDiff(oldResult.body, newResult.body, rule);

    // 创建完整报告
    const report: DiffReport = {
      id: 'parallel_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        headers: headers,
        body: requestBody,
        newUrl: newUrl  // 保存实际转换后的URL，而不是规则模板URL
      },
      responses: {
        old: oldResult,
        new: newResult
      },
      diff: diffResult,
      visualizations: {
        html: generateHtmlVisualization(oldResult, newResult, diffResult, rule),
        summary: generateSummary(oldResult, newResult, diffResult)
      }
    };

    // 保存报告
    await saveReportToStorage(report);

    const totalTime = Date.now() - startTime;
    console.log(`✅ 并行对比完成，耗时 ${totalTime}ms:`, report.id);
    console.log('📊 报告已保存，包含新接口URL:', report.request.newUrl);

  } catch (error) {
    console.error('❌ 并行对比失败:', error);

    // 即使在错误情况下，也要尝试生成新URL以便记录
    let errorNewUrl: string | undefined;
    try {
      errorNewUrl = transformUrl(details.url, rule);
      console.log('🔄 错误情况下生成的新URL:', errorNewUrl);
    } catch (urlError) {
      console.warn('⚠️ 错误情况下无法生成新URL:', urlError);
    }

    // 创建错误报告
    const errorReport: DiffReport = {
      id: 'error_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        headers: {},
        body: details.requestBody,
        newUrl: errorNewUrl  // 在错误报告中也包含新接口URL
      },
      responses: {
        old: { status: 0, body: null, responseTime: 0, error: '对比过程出错' },
        new: { status: 0, body: null, responseTime: 0, error: (error as any)?.message || '未知错误' }
      },
      diff: {
        delta: null,
        hasChanges: true,
        changeCount: 1,
        severity: 'critical'
      },
      visualizations: {
        html: `<div class="error-report"><h4>对比失败</h4><p>错误: ${(error as any)?.message || '未知错误'}</p></div>`,
        summary: `对比失败: ${(error as any)?.message || '未知错误'}`
      }
    };

    await saveReportToStorage(errorReport);
  }
}

// URL转换
function transformUrl(originalUrl: string, rule: InterceptRule): string {
  let newUrl = rule.transformation.newUrl;

  // 处理参数映射
  if (rule.transformation.paramMapping) {
    const url = new URL(originalUrl);
    const newUrlObj = new URL(newUrl);

    // 应用参数映射
    for (const [oldParam, newParam] of Object.entries(rule.transformation.paramMapping)) {
      const value = url.searchParams.get(oldParam);
      if (value !== null) {
        newUrlObj.searchParams.set(newParam, value);
        if (!rule.transformation.preserveOriginalParams) {
          url.searchParams.delete(oldParam);
        }
      }
    }

    // 保留原始参数（如果配置了）
    if (rule.transformation.preserveOriginalParams) {
      for (const [key, value] of url.searchParams.entries()) {
        if (!newUrlObj.searchParams.has(key)) {
          newUrlObj.searchParams.set(key, value);
        }
      }
    }

    newUrl = newUrlObj.toString();
  }

  // 添加内部标记，防止死循环
  const finalUrl = new URL(newUrl);
  finalUrl.searchParams.set('__api_migration_internal__', 'true');

  return finalUrl.toString();
}

// 请求头转换
function transformHeaders(originalHeaders: Record<string, string>, rule: InterceptRule): Record<string, string> {
  const headers = { ...originalHeaders };

  // 移除可能导致问题的头部
  delete headers['content-length'];
  delete headers['host'];

  // 如果启用了Cookie包含，尝试处理跨域Cookie
  if (rule.transformation.includeCookies) {
    // 保留原始Cookie头
    if (headers['cookie']) {
      console.log('🍪 保留原始Cookie:', headers['cookie'].substring(0, 50) + '...');
    }
    
    // 尝试从浏览器获取目标域的Cookie（如果权限允许）
    try {
      // 这里可以添加获取特定域Cookie的逻辑
      // 注意：需要在manifest.json中声明相应权限
    } catch (error) {
      console.warn('⚠️ 无法获取目标域Cookie:', error);
    }
  }

  return headers;
}

// 发送HTTP请求
async function makeRequest(url: string, method: string, headers: Record<string, string>, body: any, rule: InterceptRule) {
  const startTime = Date.now();

  try {
    const requestOptions: RequestInit = {
      method: method,
      headers: headers,
      // 对于跨域请求，使用 'include' 确保携带Cookie
      credentials: rule.transformation.includeCookies ? 'include' : 'omit',
      // 添加跨域相关配置
      mode: 'cors', // 明确指定CORS模式
      redirect: 'follow' // 跟随重定向
    };

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    // 详细的请求配置日志
    console.log('🚀 发送请求配置:', {
      url,
      method,
      credentials: requestOptions.credentials,
      mode: requestOptions.mode,
      cookieCount: Object.keys(headers).filter(h => h.toLowerCase().includes('cookie')).length,
      hasCookieHeader: !!headers.cookie,
      cookieHeaderValue: headers.cookie ? headers.cookie.substring(0, 100) + '...' : 'null',
      includeCookies: rule.transformation.includeCookies,
      autoCopyCookies: rule.transformation.autoCopyCookies
    });
    
    // 在发送请求前，验证目标域的Cookie状态
    if (rule.transformation.includeCookies && url.includes('127.0.0.1:8010')) {
      try {
        const targetDomain = '127.0.0.1:8010';
        const targetCookies = await browser.cookies.getAll({ domain: targetDomain });
        console.log('🔍 请求前目标域Cookie状态:', {
          targetDomain,
          cookieCount: targetCookies.length,
          cookieNames: targetCookies.map((c: any) => c.name)
        });
      } catch (cookieCheckError) {
        console.warn('⚠️ 检查目标域Cookie失败:', cookieCheckError);
      }
    }

    console.log('🌐 正在发送fetch请求:', {
      url,
      method,
      requestHeaders: requestOptions.headers,
      credentials: requestOptions.credentials,
      mode: requestOptions.mode
    });
    
    const response = await fetch(url, requestOptions);
    const responseTime = Date.now() - startTime;
    
    console.log('📡 fetch请求响应:', {
      url,
      status: response.status,
      statusText: response.statusText,
      responseTime,
      responseHeaders: Object.fromEntries(response.headers.entries()),
      responseCookies: response.headers.get('set-cookie')
    });

    let responseBody;
    const contentType = response.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      try {
        responseBody = await response.json();
      } catch {
        responseBody = await response.text();
      }
    } else {
      responseBody = await response.text();
    }

    return {
      status: response.status,
      body: responseBody,
      responseTime: responseTime
    };

  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    console.error('❌ 请求失败详情:', {
      url,
      error: error?.message,
      stack: error?.stack,
      isCorsError: error?.message?.includes('CORS'),
      isNetworkError: error?.message?.includes('NetworkError')
    });
    
    // 构建错误对象
    const errorObj = {
      status: 0,
      body: null,
      responseTime: responseTime,
      error: error?.message || '请求失败',
      isCorsError: error?.message?.includes('CORS'),
      isNetworkError: error?.message?.includes('NetworkError')
    };
    
    // 处理CORS错误
    const handledError = await handleCorsError(errorObj, rule);
    throw handledError;
  }
}

// 生成差异对比
function generateDiff(oldData: any, newData: any, rule: InterceptRule) {
  try {
    // 如果配置了忽略字段，先处理数据
    let processedOldData = oldData;
    let processedNewData = newData;

    if (rule.diffConfig?.ignoreFields && Array.isArray(rule.diffConfig.ignoreFields)) {
      processedOldData = removeIgnoredFields(oldData, rule.diffConfig.ignoreFields);
      processedNewData = removeIgnoredFields(newData, rule.diffConfig.ignoreFields);
    }

    // 使用jsondiffpatch生成差异
    const delta = differ.diff(processedOldData, processedNewData);

    // 计算变更统计
    const changeCount = countChanges(delta);
    const hasChanges = changeCount > 0;
    const severity = calculateSeverity(changeCount, oldData, newData);

    return {
      delta: delta,
      hasChanges: hasChanges,
      changeCount: changeCount,
      severity: severity
    };

  } catch (error) {
    console.error('生成差异对比失败:', error);
    return {
      delta: null,
      hasChanges: true,
      changeCount: 1,
      severity: 'critical' as const
    };
  }
}

// 移除忽略的字段
function removeIgnoredFields(data: any, ignoreFields: string[]): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => removeIgnoredFields(item, ignoreFields));
  }

  const result = { ...data };
  for (const field of ignoreFields) {
    if (field.includes('.')) {
      // 处理嵌套字段，如 'user.id'
      const parts = field.split('.');
      let current = result;
      for (let i = 0; i < parts.length - 1; i++) {
        if (current && typeof current === 'object' && current[parts[i]]) {
          current = current[parts[i]];
        } else {
          break;
        }
      }
      if (current && typeof current === 'object') {
        delete current[parts[parts.length - 1]];
      }
    } else {
      delete result[field];
    }
  }

  return result;
}

// 计算变更数量
function countChanges(delta: any): number {
  if (!delta) return 0;

  let count = 0;

  function traverse(obj: any) {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (Array.isArray(obj[key]) && obj[key].length === 2) {
          // 这是一个变更 [oldValue, newValue]
          count++;
        } else if (Array.isArray(obj[key]) && obj[key].length === 1) {
          // 这是一个删除 [oldValue]
          count++;
        } else if (Array.isArray(obj[key]) && obj[key].length === 3 && obj[key][2] === 0) {
          // 这是一个添加 [newValue, 0, 0]
          count++;
        } else {
          traverse(obj[key]);
        }
      }
    }
  }

  traverse(delta);
  return count;
}

// 计算严重程度
function calculateSeverity(changeCount: number, oldData: any, newData: any): 'none' | 'minor' | 'major' | 'critical' {
  if (changeCount === 0) return 'none';

  // 检查是否有错误状态
  if ((oldData && oldData.error) || (newData && newData.error)) {
    return 'critical';
  }

  if (changeCount <= 2) return 'minor';
  if (changeCount <= 10) return 'major';
  return 'critical';
}

// 生成HTML可视化
function generateHtmlVisualization(oldResult: any, newResult: any, diffResult: any, rule: InterceptRule): string {
  const severityClass = diffResult.severity;
  const severityMap: Record<string, string> = {
    'none': '无差异',
    'minor': '轻微差异',
    'major': '重要差异',
    'critical': '严重差异'
  };
  const severityText = severityMap[diffResult.severity] || '未知';

  return `
    <div class="api-comparison-report ${severityClass}">
      <div class="report-header">
        <h3>API对比报告</h3>
        <div class="rule-info">
          <span class="rule-name">${rule.name}</span>
          <span class="severity ${severityClass}">${severityText}</span>
        </div>
      </div>

      <div class="response-comparison">
        <div class="response-section">
          <h4>原始API响应</h4>
          <div class="status">状态码: ${oldResult.status}</div>
          <div class="response-time">响应时间: ${oldResult.responseTime}ms</div>
          ${oldResult.error ? `<div class="error">错误: ${oldResult.error}</div>` : ''}
          <pre class="response-body">${JSON.stringify(oldResult.body, null, 2)}</pre>
        </div>

        <div class="response-section">
          <h4>新API响应</h4>
          <div class="status">状态码: ${newResult.status}</div>
          <div class="response-time">响应时间: ${newResult.responseTime}ms</div>
          ${newResult.error ? `<div class="error">错误: ${newResult.error}</div>` : ''}
          <pre class="response-body">${JSON.stringify(newResult.body, null, 2)}</pre>
        </div>
      </div>

      <div class="diff-section">
        <h4>差异详情</h4>
        <div class="diff-stats">
          <span>变更数量: ${diffResult.changeCount}</span>
          <span>严重程度: ${severityText}</span>
        </div>
        ${diffResult.delta ? `<pre class="diff-delta">${JSON.stringify(diffResult.delta, null, 2)}</pre>` : '<p>无差异</p>'}
      </div>
    </div>
  `;
}

// 生成摘要
function generateSummary(oldResult: any, newResult: any, diffResult: any): string {
  if (diffResult.changeCount === 0) {
    return `✅ API响应一致 (${oldResult.responseTime}ms vs ${newResult.responseTime}ms)`;
  }

  if (oldResult.error || newResult.error) {
    return `❌ API请求失败: ${oldResult.error || newResult.error}`;
  }

  const timeDiff = Math.abs(oldResult.responseTime - newResult.responseTime);
  return `⚠️ 发现 ${diffResult.changeCount} 处差异，响应时间差异 ${timeDiff}ms`;
}
